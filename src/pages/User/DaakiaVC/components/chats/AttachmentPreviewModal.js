import { useState } from 'react';
import { Modal } from 'antd';
import { MdFileDownload, MdOutlineZoomIn, MdOutlineZoomOut, MdZoomInMap } from 'react-icons/md';
import { Document, Page } from 'react-pdf';

/**
 * Reusable Attachment Preview Modal Component
 * Supports both PDF and Image previews with zoom functionality
 */
export function AttachmentPreviewModal({
  isOpen,
  onClose,
  entry,
  canDownloadChatAttachment = true,
}) {
  const [scale, setScale] = useState(1);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [startPos, setStartPos] = useState({ x: 0, y: 0 });

  // PDF-specific state
  const [numPages, setNumPages] = useState(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [pdfScale, setPdfScale] = useState(1.0);

  // Zoom controls for images
  const zoomIn = () => setScale((prev) => Math.min(prev + 0.2, 3)); // Max zoom level: 3x
  const zoomOut = () => setScale((prev) => Math.max(prev - 0.2, 1)); // Min zoom level: 1x
  const resetZoom = () => setScale(1); // Reset zoom to default

  // PDF-specific functions
  const onDocumentLoadSuccess = ({ numPages: totalPages }) => {
    setNumPages(totalPages);
    setPageNumber(1);
  };

  // PDF zoom controls
  const pdfZoomIn = () => setPdfScale((prev) => Math.min(prev + 0.2, 3));
  const pdfZoomOut = () => setPdfScale((prev) => Math.max(prev - 0.2, 0.5));
  const resetPdfZoom = () => setPdfScale(1.0);

  // PDF navigation
  const goToPrevPage = () => setPageNumber((prev) => Math.max(prev - 1, 1));
  const goToNextPage = () => setPageNumber((prev) => Math.min(prev + 1, numPages || 1));

  // Drag controls for images
  const handleMouseDown = (e) => {
    setIsDragging(true);
    setStartPos({ x: e.clientX - position.x, y: e.clientY - position.y });
  };

  const handleMouseMove = (e) => {
    if (isDragging) {
      setPosition({ x: e.clientX - startPos.x, y: e.clientY - startPos.y });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleDownload = () => {
    window.open(entry.message.split(" ")[0], "_blank");
  };

  const handleClose = () => {
    setPosition({ x: 0, y: 0 });
    setScale(1);
    setIsDragging(false);
    // Reset PDF state
    setPdfScale(1.0);
    setPageNumber(1);
    setNumPages(null);
    onClose();
  };

  if (!entry || !isOpen) return null;

  const fileUrl = entry.message.split(" ")[0];
  const fileName = entry.message.split("-file-").pop();
  const isPDF = /pdf/.test(entry.message);
  const isImage = /jpeg|jpg|png|gif/.test(entry.message);

  return (
    <Modal
      open={isOpen}
      onCancel={handleClose}
      footer={null}
      className="lk-attatchment-preview"
      maskStyle={{
        background: "rgba(0, 0, 0, 0.7)",
        backdropFilter: "blur(4px)",
      }}

      width="50%"
      centered
    >
      {/* Download Button */}
      {canDownloadChatAttachment && (
        <MdFileDownload
          onClick={handleDownload}
          className="attatchment-download"
        />
      )}
      
      {/* File Name */}
      <span className="attatchment-name">
        {fileName}
      </span>

      {/* PDF Preview with frame-like container and overlay controls */}
      {isPDF && (
        <div
          className="lk-chat-pdf-frame-container"
          style={{
            position: "relative",
            width: "100%",
            height: "500px",
            border: "1px solid #ddd",
            borderRadius: "4px",
            overflow: "hidden",
            backgroundColor: "#f5f5f5"
          }}
        >
          {/* PDF Content Area */}
          <div
            style={{
              width: "100%",
              height: "100%",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              overflow: "auto",
              backgroundColor: "#525659"
            }}
          >
            <Document
              file={fileUrl}
              onLoadSuccess={onDocumentLoadSuccess}
              loading={<div style={{ color: "white", padding: "20px" }}>Loading PDF...</div>}
              error={
                <div style={{ color: "white", padding: "20px", textAlign: "center" }}>
                  Failed to load PDF.
                  <br />
                  <a
                    href={fileUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    style={{ color: "#4CAF50", textDecoration: "underline" }}
                  >
                    Open in new tab
                  </a>
                </div>
              }
              className="lk-chat-pdf-document"
            >
              <Page
                pageNumber={pageNumber}
                scale={pdfScale}
                className="lk-chat-pdf-page"
                renderAnnotationLayer={false}
                renderTextLayer={false}
              />
            </Document>
          </div>

          {/* Overlay Controls - Top Bar */}
          <div
            className="lk-chat-pdf-controls-overlay"
            style={{
              position: "absolute",
              top: "0",
              left: "0",
              right: "0",
              background: "rgba(0, 0, 0, 0.8)",
              color: "white",
              padding: "8px 12px",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              fontSize: "14px",
              zIndex: 10
            }}
          >
            {/* Left side - Page info */}
            <div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
              {numPages && numPages > 1 && (
                <>
                  <button
                    onClick={goToPrevPage}
                    disabled={pageNumber <= 1}
                    style={{
                      background: "transparent",
                      border: "1px solid rgba(255,255,255,0.3)",
                      color: pageNumber <= 1 ? "#666" : "white",
                      padding: "4px 8px",
                      borderRadius: "3px",
                      cursor: pageNumber <= 1 ? "not-allowed" : "pointer",
                      fontSize: "12px"
                    }}
                  >
                    ‹ Prev
                  </button>
                  <span style={{ fontSize: "13px" }}>
                    {pageNumber} / {numPages}
                  </span>
                  <button
                    onClick={goToNextPage}
                    disabled={pageNumber >= numPages}
                    style={{
                      background: "transparent",
                      border: "1px solid rgba(255,255,255,0.3)",
                      color: pageNumber >= numPages ? "#666" : "white",
                      padding: "4px 8px",
                      borderRadius: "3px",
                      cursor: pageNumber >= numPages ? "not-allowed" : "pointer",
                      fontSize: "12px"
                    }}
                  >
                    Next ›
                  </button>
                </>
              )}
            </div>

            {/* Right side - Zoom controls */}
            <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
              <MdOutlineZoomOut
                onClick={pdfZoomOut}
                style={{
                  cursor: "pointer",
                  fontSize: "18px",
                  opacity: pdfScale <= 0.5 ? 0.5 : 1
                }}
                title="Zoom Out"
              />
              <span style={{ fontSize: "12px", minWidth: "40px", textAlign: "center" }}>
                {Math.round(pdfScale * 100)}%
              </span>
              <MdOutlineZoomIn
                onClick={pdfZoomIn}
                style={{
                  cursor: "pointer",
                  fontSize: "18px",
                  opacity: pdfScale >= 3 ? 0.5 : 1
                }}
                title="Zoom In"
              />
              <MdZoomInMap
                onClick={resetPdfZoom}
                style={{
                  cursor: "pointer",
                  fontSize: "18px",
                  marginLeft: "4px"
                }}
                title="Reset Zoom"
              />
            </div>
          </div>
        </div>
      )}

      {/* Image Preview with Zoom */}
      {isImage && (
        <div
          className="lk-chat-image-preview-container"
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
          style={{
            cursor: isDragging ? "grabbing" : "grab",
            overflow: "hidden",
            position: "relative",
            height: "400px",
            background: "transparent",
          }}
        >
          <img
            src={fileUrl}
            alt="Attachment Preview"
            style={{
              width: "100%",
              transform: `translate(${position.x}px, ${position.y}px) scale(${scale})`,
              transition: isDragging ? "none" : "transform 0.2s ease-in-out",
              cursor: isDragging ? "grabbing" : "grab",
            }}
            onMouseDown={handleMouseDown}
            draggable={false}
          />
          
          {/* Zoom Controls */}
          <MdOutlineZoomIn
            onClick={zoomIn}
            className="attatchment-zoom-in"
          />
          <MdOutlineZoomOut
            onClick={zoomOut}
            className="attatchment-zoom-out"
          />
          <MdZoomInMap
            onClick={resetZoom}
            className="attatchment-zoom-reset"
          />
        </div>
      )}
    </Modal>
  );
}

export default AttachmentPreviewModal;
